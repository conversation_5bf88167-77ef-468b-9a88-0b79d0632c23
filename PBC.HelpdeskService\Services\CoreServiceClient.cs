using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using PBC.HelpdeskService.Models;
using System.Text;

namespace PBC.HelpdeskService.Services
{
    /// <summary>
    /// Client for communicating with PBC.CoreService
    /// </summary>
    public class CoreServiceClient : ICoreServiceClient
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CoreServiceClient> _logger;
        private readonly string _coreServiceUrl;

        public CoreServiceClient(HttpClient httpClient, IConfiguration configuration, ILogger<CoreServiceClient> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            _coreServiceUrl = _configuration["ServiceUrls:CoreService"] ?? "http://localhost:5001";
        }

        /// <inheritdoc/>
        public async Task<StringBuilder[]> CommonMethodForEmailandSMSAsync(string connString, CommonMethodForEmailandSMSList obj)
        {
            try
            {
                _logger.LogInformation("Calling core service for CommonMethodForEmailandSMS with template code: {TemplateCode}", obj.TemplateCode);

                // Create request with connection string and log exception
                var request = new CommonMethodForEmailandSMSRequest
                {
                    TemplateCode = obj.TemplateCode,
                    CompanyId = obj.CompanyId,
                    LanguageCode = obj.LanguageCode,
                    BranchId = obj.BranchId,
                    p1 = obj.p1,
                    p2 = obj.p2,
                    p3 = obj.p3,
                    p4 = obj.p4,
                    p5 = obj.p5,
                    p6 = obj.p6,
                    p7 = obj.p7,
                    p8 = obj.p8,
                    p9 = obj.p9,
                    p10 = obj.p10,
                    p11 = obj.p11,
                    p12 = obj.p12,
                    p13 = obj.p13,
                    p14 = obj.p14,
                    p15 = obj.p15,
                    p16 = obj.p16,
                    p17 = obj.p17,
                    p18 = obj.p18,
                    p19 = obj.p19,
                    p20 = obj.p20,
                    ConnString = connString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_coreServiceUrl}/api/coreemailtemplate/Common-Method-ForEmailandSMS", content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogWarning(
                "Failed to call CommonMethodForEmailandSMS. Status: {StatusCode}",
                response.StatusCode);
                    return Array.Empty<StringBuilder>();
                }
                var payload = await response.Content.ReadAsStringAsync();

                var result = JsonConvert
                    .DeserializeObject<StringBuilder[]>(payload);

                return result ?? Array.Empty<StringBuilder>();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                            ex,
                            "Error calling core service for CommonMethodForEmailandSMS");
                return Array.Empty<StringBuilder>();
            }
        }

        // Request model for CoreService communication ove to DTOs
        public class CommonMethodForEmailandSMSRequest
        {
            public string TemplateCode { get; set; }
            public int CompanyId { get; set; }
            public string LanguageCode { get; set; }
            public int BranchId { get; set; }
            public string p1 { get; set; }
            public string p2 { get; set; }
            public string p3 { get; set; }
            public string p4 { get; set; }
            public string p5 { get; set; }
            public string p6 { get; set; }
            public string p7 { get; set; }
            public string p8 { get; set; }
            public string p9 { get; set; }
            public string p10 { get; set; }
            public string p11 { get; set; }
            public string p12 { get; set; }
            public string p13 { get; set; }
            public string p14 { get; set; }
            public string p15 { get; set; }
            public string p16 { get; set; }
            public string p17 { get; set; }
            public string p18 { get; set; }
            public string p19 { get; set; }
            public string p20 { get; set; }
            public string ConnString { get; set; }
        }
    }
}
