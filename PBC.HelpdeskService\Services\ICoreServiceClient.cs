using Microsoft.AspNetCore.Mvc;
using PBC.HelpdeskService.Models;
using System.Text;

namespace PBC.HelpdeskService.Services
{
    /// <summary>
    /// Interface for communicating with PBC.CoreService
    /// </summary>
    public interface ICoreServiceClient
    {
        /// <summary>
        /// Get common method for email and SMS using core service
        /// </summary>
        // Task<IActionResult> CommonMethodForEmailandSMSAsync(CommonMethodForEmailandSMSList obj, string connString, int logException);
        Task<StringBuilder[]> CommonMethodForEmailandSMSAsync(string connString,CommonMethodForEmailandSMSList obj);

    }
}
